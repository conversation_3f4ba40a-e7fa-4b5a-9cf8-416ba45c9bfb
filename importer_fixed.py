import os
import pymysql
from openpyxl import load_workbook
import time

# 数据库连接信息
DB_CONFIG = {
    'host': '************',
    'user': 'lxt',
    'password': 'Lxt0307+',
    'database': 'qq_day_sale'
}

# 目标表名
TARGET_TABLE = 'wdt_saledetail'

# 数据目录
DATA_FOLDER = 'data'

# 单线程处理模式

# Excel文件的列名（92列）
EXCEL_COLUMNS = [
    '订单编号', '原始单号', '子单原始单号', '原始子订单号', '订单类型', '支付账号',
    '出库单编号', '仓库', '仓库类型', '店铺', '出库单状态', '出库状态', '分拣序号',
    '商家编码', '货品编号', '货品名称', '货品简称', '品牌', '分类', '规格码',
    '规格名称', '平台货品名称', '平台规格名称', '平台货品ID', '平台规格ID', '条形码',
    '货品数量', '货品原单价', '货品原总金额', '订单总优惠', '邮费', '货品成交价',
    '货品成交总价', '货品总优惠', '货到付款金额', '货品成本', '货品总成本', '固定成本',
    '固定总成本', '订单支付金额', '应收金额', '退款前支付金额', '单品支付金额', '分摊邮费',
    '预估邮资', '邮资成本', '订单包装成本', '订单毛利', '毛利率', '订单固定毛利',
    '固定毛利率', '客户网名', '收件人', '证件号码', '收货地区', '收货地址',
    '收件人手机', '收件人电话', '物流公司', '实际重量', '预估重量', '需开发票',
    '制单人', '打单员', '拣货员', '打包员', '检视员', '业务员', '验货员',
    '打印波次', '物流单打印状态', '发货单打印状态', '分拣单打印状态', '物流单号',
    '分拣单编号', '外部单号', '付款时间', '发货时间', '赠品方式', '买家留言',
    '客服备注', '打印备注', '备注', '包装', '来源组合装编码', '拆自组合装',
    '来源组合装数量', '体积', '分销商', '分销商编号', '下单时间', '审核时间'
]

# 数据库表的列名（93列，不包括自增主键id）
DB_COLUMNS = [
    '订单编号', '原始单号', '子单原始单号', '原始子订单号', '订单类型', '支付账号',
    '出库单编号', '仓库', '仓库类型', '店铺', '出库单状态', '出库状态', '分拣序号',
    '商家编码', '货品编号', '货品名称', '货品简称', '品牌', '分类', '规格码',
    '规格名称', '平台货品名称', '平台规格名称', '平台货品ID', '平台规格ID', '条形码',
    '货品数量', '货品原单价', '货品原总金额', '订单总优惠', '邮费', '货品成交价',
    '货品成交总价', '货品总优惠', '货到付款金额', '货品成本', '货品总成本', '固定成本',
    '固定总成本', '订单支付金额', '应收金额', '退款前支付金额', '单品支付金额', '分摊邮费',
    '预估邮资', '邮资成本', '订单包装成本', '订单毛利', '毛利率', '订单固定毛利',
    '固定毛利率', '客户网名', '收件人', '证件号码', '收货地区', '收货地址',
    '收件人手机', '收件人电话', '物流公司', '实际重量', '预估重量', '需开发票',
    '制单人', '打单员', '拣货员', '打包员', '检视员', '业务员', '验货员',
    '打印波次', '物流单打印状态', '发货单打印状态', '分拣单打印状态', '物流单号',
    '分拣单编号', '外部单号', '付款时间', '发货时间', '下单时间', '审核时间',
    '赠品方式', '买家留言', '客服备注', '打印备注', '备注', '包装',
    '来源组合装编码', '拆自组合装', '来源组合装数量', '体积', '分销商', '分销商编号',
    '分销原始单号'
]

def get_db_connection():
    """创建数据库连接"""
    return pymysql.connect(**DB_CONFIG)

def process_excel_file(file_path):
    """处理单个Excel文件"""
    try:
        print(f"正在处理文件: {os.path.basename(file_path)}")

        # 创建数据库连接
        connection = get_db_connection()
        cursor = connection.cursor()

        # 加载Excel文件
        wb = load_workbook(file_path)
        ws = wb.active

        # 批量插入数据
        batch_data = []
        batch_size = 1000  # 每批次插入1000条数据

        for row in ws.iter_rows(min_row=2, values_only=True):
            if row and any(cell is not None for cell in row):  # 跳过空行
                # 将Excel行数据转换为数据库格式（添加缺失的列）
                db_row = convert_excel_row_to_db_row(row)
                batch_data.append(db_row)

                if len(batch_data) >= batch_size:
                    insert_batch_data(cursor, batch_data)
                    batch_data = []

        # 插入剩余数据
        if batch_data:
            insert_batch_data(cursor, batch_data)

        # 提交事务
        connection.commit()
        cursor.close()
        connection.close()

        print(f"完成处理文件: {os.path.basename(file_path)}")
        return f"✅ 成功处理: {os.path.basename(file_path)}"

    except Exception as e:
        print(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
        return f"❌ 处理失败: {os.path.basename(file_path)} - {str(e)}"

def convert_excel_row_to_db_row(excel_row):
    """将Excel行数据转换为数据库行数据"""
    # 确保Excel行有92列
    excel_data = list(excel_row)
    while len(excel_data) < 92:
        excel_data.append(None)

    # 创建数据库行数据（93列）
    db_row = []

    # 定义需要特殊处理的日期时间字段
    datetime_fields = {'付款时间', '发货时间', '下单时间', '审核时间'}

    # 定义需要特殊处理的decimal字段（可能为空的字段）
    decimal_fields = {
        '货品原单价', '货品原总金额', '订单总优惠', '邮费', '货品成交价',
        '货品成交总价', '货品总优惠', '货到付款金额', '货品成本', '货品总成本',
        '固定成本', '固定总成本', '订单支付金额', '应收金额', '退款前支付金额',
        '单品支付金额', '分摊邮费', '预估邮资', '邮资成本',
        '订单毛利', '毛利率', '订单固定毛利', '固定毛利率', '实际重量',
        '预估重量', '体积'
    }

    # 特别需要处理的可能为空的decimal字段（根据用户要求）
    nullable_decimal_fields = {
        '货品成本', '货品总成本', '固定成本', '固定总成本', '预估邮资',
        '邮资成本', '订单毛利', '毛利率', '订单固定毛利', '固定毛利率',
        '货品原单价', '货品总优惠', '退款前支付金额'
    }

    # 按照数据库列的顺序填充数据
    for db_col in DB_COLUMNS:
        if db_col in EXCEL_COLUMNS:
            # 找到Excel中对应列的索引
            excel_index = EXCEL_COLUMNS.index(db_col)
            value = excel_data[excel_index]

            # 对日期时间字段进行特殊处理
            if db_col in datetime_fields:
                # 如果是空字符串，转换为None（数据库NULL）
                if value == '' or value is None:
                    value = None
                # 如果是字符串但不是空字符串，保持原值（让数据库自己解析）
                # 如果是datetime对象，保持原值

            # 对decimal字段进行特殊处理
            elif db_col in decimal_fields:
                # 对于可能为空的decimal字段，进行更严格的空值检查
                if db_col in nullable_decimal_fields:
                    # 检查各种空值情况
                    if (value is None or
                        value == '' or
                        (isinstance(value, str) and value.strip() == '') or
                        (isinstance(value, str) and value.strip().lower() in ['null', 'none', 'n/a', '-'])):
                        # 调试信息：记录空值转换
                        if value is not None and value != '':
                            print(f"调试: 字段 '{db_col}' 的值 '{value}' 被识别为空值，已转换为NULL")
                        value = None
                    # 如果是字符串，尝试转换为float验证是否为有效数字
                    elif isinstance(value, str):
                        try:
                            # 去除前后空格
                            cleaned_value = value.strip()
                            # 尝试转换为float来验证是否为有效数字
                            float(cleaned_value)
                            # 如果转换成功，保持原字符串值（让数据库处理）
                            value = cleaned_value
                        except (ValueError, AttributeError):
                            # 如果转换失败，说明不是有效数字，设为None
                            print(f"警告: 字段 '{db_col}' 的值 '{value}' 不是有效数字，已设为NULL")
                            value = None
                    # 如果是数字类型，保持原值
                else:
                    # 对于其他decimal字段，使用原来的处理逻辑
                    if value == '' or value is None or (isinstance(value, str) and value.strip() == ''):
                        value = None
                    elif isinstance(value, str):
                        try:
                            float(value.strip())
                            value = value.strip()
                        except (ValueError, AttributeError):
                            print(f"警告: 字段 '{db_col}' 的值 '{value}' 不是有效数字，已设为NULL")
                            value = None

            db_row.append(value)
        else:
            # 缺失的列填充None（对应数据库的NULL）
            db_row.append(None)

    return tuple(db_row)

def insert_batch_data(cursor, batch_data):
    """批量插入数据"""
    if not batch_data:
        return

    # 构建指定列名的INSERT语句
    column_names = ', '.join([f'`{col}`' for col in DB_COLUMNS])
    placeholders = ', '.join(['%s'] * len(DB_COLUMNS))
    sql = f"INSERT INTO {TARGET_TABLE} ({column_names}) VALUES ({placeholders})"

    try:
        cursor.executemany(sql, batch_data)
        print(f"成功批量插入 {len(batch_data)} 条数据")
    except Exception as e:
        print(f"批量插入数据时出错: {str(e)}")
        # 如果批量插入失败，尝试逐行插入
        for i, row in enumerate(batch_data):
            try:
                cursor.execute(sql, row)
                if (i + 1) % 100 == 0:
                    print(f"已逐行插入 {i + 1} 条数据")
            except Exception as row_error:
                print(f"插入行数据失败: {row} - {str(row_error)}")

def main():
    """主函数"""
    start_time = time.time()

    # 检查数据目录是否存在
    if not os.path.exists(DATA_FOLDER):
        print(f"错误: 数据目录 '{DATA_FOLDER}' 不存在")
        return

    # 获取所有Excel文件
    excel_files = [f for f in os.listdir(DATA_FOLDER)
                   if f.endswith('.xlsx') or f.endswith('.xls')]

    if not excel_files:
        print(f"在目录 '{DATA_FOLDER}' 中没有找到Excel文件")
        return

    # 处理所有Excel文件
    print(f"找到 {len(excel_files)} 个Excel文件，将使用单线程顺序处理")
    print(f"文件列表: {excel_files[:5]}{'...' if len(excel_files) > 5 else ''}")  # 显示前5个文件名

    # 构建完整文件路径
    file_paths = [os.path.join(DATA_FOLDER, file) for file in excel_files]

    # 单线程顺序处理文件
    results = []
    for i, file_path in enumerate(file_paths, 1):
        print(f"进度: {i}/{len(excel_files)} - 开始处理文件: {os.path.basename(file_path)}")
        try:
            result = process_excel_file(file_path)
            results.append(result)
            print(f"进度: {i}/{len(excel_files)} 文件已完成")
        except Exception as exc:
            error_msg = f"❌ 文件 {os.path.basename(file_path)} 处理异常: {exc}"
            print(error_msg)
            results.append(error_msg)

    end_time = time.time()

    # 打印处理结果
    print("\n" + "="*50)
    print("处理结果汇总:")
    for result in results:
        print(f"  {result}")

    print(f"\n总共处理 {len(excel_files)} 个文件")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print(f"平均每文件耗时: {(end_time - start_time) / len(excel_files):.2f} 秒")
    print("="*50)

if __name__ == "__main__":
    main()
