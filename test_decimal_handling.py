#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 decimal 字段空值处理的脚本
"""

import sys
import os

# 添加当前目录到路径，以便导入 importer_fixed
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from importer_fixed import convert_excel_row_to_db_row, EXCEL_COLUMNS, DB_COLUMNS

def test_decimal_null_handling():
    """测试 decimal 字段的空值处理"""
    print("测试 decimal 字段空值处理...")
    print("="*60)
    
    # 创建一个测试行，包含各种空值情况
    test_row = [''] * 92  # 创建92列的空行
    
    # 设置一些基本字段
    test_row[0] = 'TEST001'  # 订单编号
    test_row[1] = 'ORIG001'  # 原始单号
    
    # 测试需要特殊处理的 decimal 字段的各种空值情况
    test_cases = [
        ('货品成本', ''),           # 空字符串
        ('货品总成本', None),       # None值
        ('固定成本', '  '),         # 空白字符串
        ('固定总成本', 'null'),     # 'null'字符串
        ('预估邮资', 'N/A'),        # 'N/A'字符串
        ('邮资成本', '-'),          # '-'字符串
        ('订单毛利', '123.45'),     # 有效数字
        ('毛利率', 'abc'),          # 无效数字
        ('订单固定毛利', '0'),      # 零值
        ('固定毛利率', '0.00'),     # 零值小数
        ('货品原单价', '  123.45  '), # 带空格的有效数字
        ('货品总优惠', 'none'),     # 'none'字符串
        ('退款前支付金额', 'NONE'), # 'NONE'字符串（大写）
    ]
    
    for field_name, test_value in test_cases:
        if field_name in EXCEL_COLUMNS:
            field_index = EXCEL_COLUMNS.index(field_name)
            test_row[field_index] = test_value
    
    # 转换行数据
    db_row = convert_excel_row_to_db_row(test_row)
    
    # 检查结果
    print("测试结果:")
    print("-" * 60)
    
    for field_name, expected_input in test_cases:
        if field_name in DB_COLUMNS:
            db_index = DB_COLUMNS.index(field_name)
            actual_output = db_row[db_index]
            
            print(f"字段: {field_name}")
            print(f"  输入值: {repr(expected_input)}")
            print(f"  输出值: {repr(actual_output)}")
            print(f"  是否为NULL: {actual_output is None}")
            print()
    
    print("="*60)
    print("测试完成！")

def test_valid_decimal_values():
    """测试有效的 decimal 值是否正确保留"""
    print("\n测试有效 decimal 值的保留...")
    print("="*60)
    
    test_row = [''] * 92
    test_row[0] = 'TEST002'  # 订单编号
    
    # 测试有效的数字值
    valid_cases = [
        ('货品成本', '123.45'),
        ('货品总成本', '1000.00'),
        ('固定成本', '50'),
        ('预估邮资', '15.5'),
        ('订单毛利', '200.75'),
        ('毛利率', '0.25'),
    ]
    
    for field_name, test_value in valid_cases:
        if field_name in EXCEL_COLUMNS:
            field_index = EXCEL_COLUMNS.index(field_name)
            test_row[field_index] = test_value
    
    db_row = convert_excel_row_to_db_row(test_row)
    
    print("有效数字测试结果:")
    print("-" * 60)
    
    for field_name, expected_input in valid_cases:
        if field_name in DB_COLUMNS:
            db_index = DB_COLUMNS.index(field_name)
            actual_output = db_row[db_index]
            
            print(f"字段: {field_name}")
            print(f"  输入值: {repr(expected_input)}")
            print(f"  输出值: {repr(actual_output)}")
            print(f"  值是否保留: {actual_output == expected_input}")
            print()

if __name__ == "__main__":
    test_decimal_null_handling()
    test_valid_decimal_values()
